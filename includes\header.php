<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Luxury Beauty Salon' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Experience luxury and elegance at Flix Salon & SPA. Premium beauty services with a touch of sophistication.' ?>">
    <meta name="keywords" content="salon, beauty, hair, spa, luxury, styling, facial, massage, manicure, pedicure">
    <meta name="author" content="Flix Salon & SPA">

    <!-- Open Graph -->
    <meta property="og:title" content="<?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Luxury Beauty Salon' ?>">
    <meta property="og:description" content="<?= isset($pageDescription) ? $pageDescription : 'Experience luxury and elegance at Flix Salon & SPA. Premium beauty services with a touch of sophistication.' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= APP_URL ?>">
    <meta property="og:image" content="<?= APP_URL ?>/assets/images/og-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?= getBasePath() ?>/includes/flix_logo.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#1a1a1a',
                            800: '#141414',
                            900: '#0a0a0a',
                        },
                        'salon-black': '#000000',
                        'salon-gold': '#f59e0b',
                        'salon-white': '#ffffff',
                        'gold-light': '#fbbf24',
                        'gold-dark': '#d97706',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        :root {
            --background: #000000;
            --foreground: #ffffff;
            --gold: #f59e0b;
            --gold-light: #fbbf24;
            --gold-dark: #d97706;
            --black-secondary: #0a0a0a;
            --black-tertiary: #141414;
            --black-quaternary: #1a1a1a;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
            padding-top: 80px; /* Add padding for fixed header */
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--black-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gold);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gold-dark);
        }

        /* Selection */
        ::selection {
            background: var(--gold);
            color: var(--background);
        }

        /* Hero carousel transitions */
        .hero-slide {
            transition: opacity 1s ease-in-out;
        }

        .hero-dot {
            transition: all 0.3s ease;
        }

        .hero-dot.active {
            background-color: var(--gold) !important;
        }

        /* Hamburger menu animation */
        .mobile-menu-toggle {
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background-color: rgba(245, 158, 11, 0.1);
        }

        /* Dropdown menu animations */
        .group:hover .group-hover\\:opacity-100 {
            opacity: 1;
        }

        .group:hover .group-hover\\:visible {
            visibility: visible;
        }

        /* Line clamp utilities */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Refresh button animation */
        .refresh-spin {
            animation: spin 0.5s linear;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen flex flex-col public-page">
    <!-- Header -->
    <header class="bg-salon-black/98 backdrop-blur-md border-b border-secondary-700 fixed top-0 left-0 right-0 z-50">
        <nav class="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8" aria-label="Global">
            <!-- Logo -->
            <div class="flex lg:flex-1">
                <a href="<?= getBasePath() ?>/" class="-m-1.5 p-1.5">
                    <span class="text-2xl font-bold font-serif text-salon-gold">
                        Flix Salon & SPA
                    </span>
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="flex lg:hidden">
                <button type="button"
                        class="mobile-menu-toggle -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white hover:text-salon-gold transition-colors"
                        onclick="openMobileMenu()">
                    <span class="sr-only">Open main menu</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                </button>
            </div>

            <!-- Desktop navigation -->
            <div class="hidden lg:flex lg:gap-x-12">
                <?php
                $basePath = getBasePath();
                $currentPath = $_SERVER['REQUEST_URI'];

                // Get service categories for dropdown (random selection of 5)
                require_once __DIR__ . '/service_category_functions.php';
                $serviceCategories = getActiveServiceCategories();

                // Shuffle categories and take up to 5 for random display
                if (!empty($serviceCategories)) {
                    shuffle($serviceCategories);
                    $displayCategories = array_slice($serviceCategories, 0, 5);
                    $hasMoreCategories = count($serviceCategories) > 5;
                } else {
                    $displayCategories = [];
                    $hasMoreCategories = false;
                }
                ?>

                <!-- Home -->
                <?php $isActive = $currentPath === $basePath . '/'; ?>
                <a href="<?= $basePath ?>/"
                   class="text-sm font-semibold leading-6 transition-colors <?= $isActive ? 'text-salon-gold' : 'text-white hover:text-salon-gold' ?>">
                    Home
                </a>

                <!-- Services with Dropdown -->
                <?php $isServicesActive = $currentPath === $basePath . '/services' || strpos($currentPath, $basePath . '/services') === 0; ?>
                <div class="relative group">
                    <a href="<?= $basePath ?>/services"
                       class="flex items-center text-sm font-semibold leading-6 transition-colors <?= $isServicesActive ? 'text-salon-gold' : 'text-white hover:text-salon-gold' ?>">
                        Services
                        <svg class="ml-1 h-4 w-4 transition-transform group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                        </svg>
                    </a>

                    <!-- Services Dropdown -->
                    <div class="absolute top-full left-0 mt-2 w-64 bg-secondary-900 rounded-lg shadow-lg border border-secondary-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <div class="py-2">
                            <!-- Random Categories Header -->
                            <?php if (!empty($displayCategories)): ?>
                                <div class="px-4 py-2 border-b border-secondary-700">
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs font-medium text-salon-gold">✨ Featured Categories</span>
                                        <button onclick="refreshDropdownCategories()"
                                                class="text-xs text-gray-400 hover:text-salon-gold transition-colors"
                                                title="Refresh categories">
                                            <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- All Services Link -->
                            <a href="<?= $basePath ?>/services"
                               class="flex items-center px-4 py-2 text-sm text-white hover:bg-secondary-700 hover:text-salon-gold transition-colors">
                                <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6z" />
                                </svg>
                                All Services
                            </a>

                            <?php if (!empty($displayCategories)): ?>
                                <div class="border-t border-secondary-700 my-1"></div>

                                <!-- Category Links -->
                                <?php foreach ($displayCategories as $category): ?>
                                    <a href="<?= $basePath ?>/services?category=<?= urlencode($category['name']) ?>"
                                       class="flex items-center px-4 py-2 text-sm text-white hover:bg-secondary-700 hover:text-salon-gold transition-colors">
                                        <div class="w-2 h-2 bg-salon-gold rounded-full mr-3 flex-shrink-0"></div>
                                        <div class="flex-1">
                                            <div class="font-medium"><?= htmlspecialchars($category['name']) ?></div>
                                            <?php if ($category['description']): ?>
                                                <div class="text-xs text-gray-400 line-clamp-1"><?= htmlspecialchars($category['description']) ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                <?php endforeach; ?>

                                <?php if ($hasMoreCategories): ?>
                                    <div class="border-t border-secondary-700 my-1"></div>
                                    <a href="<?= $basePath ?>/services"
                                       class="flex items-center px-4 py-2 text-sm text-salon-gold hover:bg-secondary-700 transition-colors">
                                        <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                                        </svg>
                                        View More Categories
                                    </a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Other Navigation Items -->
                <?php
                $otherNavigation = [
                    ['name' => 'Packages', 'href' => $basePath . '/packages'],
                    ['name' => 'Offers', 'href' => $basePath . '/offers'],
                    ['name' => 'Gallery', 'href' => $basePath . '/gallery'],
                    ['name' => 'Contact', 'href' => $basePath . '/contact'],
                ];

                foreach ($otherNavigation as $item):
                    $isActive = $currentPath === $item['href'] || ($item['href'] !== $basePath . '/' && strpos($currentPath, $item['href']) === 0);
                ?>
                    <a href="<?= $item['href'] ?>"
                       class="text-sm font-semibold leading-6 transition-colors <?= $isActive ? 'text-salon-gold' : 'text-white hover:text-salon-gold' ?>">
                        <?= $item['name'] ?>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- Desktop CTA buttons -->
            <div class="hidden lg:flex lg:flex-1 lg:justify-end">
                <div class="flex items-center gap-x-3">
                    <!-- Wishlist Button -->
                    <?php
                    // Include wishlist functions for count
                    require_once __DIR__ . '/wishlist_functions.php';
                    $wishlistCount = isset($_SESSION['user_id'])
                        ? getWishlistCount($_SESSION['user_id'])
                        : getSessionWishlistCount();
                    ?>
                    <a href="<?= isLoggedIn() ? $basePath . '/customer/wishlist' : $basePath . '/auth/login.php?redirect=' . urlencode('/customer/wishlist') ?>"
                       class="relative inline-flex items-center rounded-lg px-3 py-2 text-sm font-semibold text-white hover:text-salon-gold transition-colors">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <?php if ($wishlistCount > 0): ?>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full min-w-[18px] text-center" id="header-wishlist-badge">
                                <?= $wishlistCount ?>
                            </span>
                        <?php endif; ?>
                    </a>

                    <!-- Book Now Button - Always visible -->
                    <?php if (isLoggedIn()): ?>
                        <a href="<?= $basePath ?>/customer/book" class="inline-flex items-center rounded-lg bg-salon-gold px-4 py-2 text-sm font-semibold text-black hover:bg-gold-light transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 712.25-2.25h13.5A2.25 2.25 0 0721 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 715.25 9h13.5a2.25 2.25 0 712.25 2.25v7.5" />
                            </svg>
                            Book Now
                        </a>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/services" class="inline-flex items-center rounded-lg border border-salon-gold px-4 py-2 text-sm font-semibold text-salon-gold hover:bg-salon-gold hover:text-black transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 712.25-2.25h13.5A2.25 2.25 0 0721 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 715.25 9h13.5a2.25 2.25 0 712.25 2.25v7.5" />
                            </svg>
                            Book Now
                        </a>
                    <?php endif; ?>

                    <!-- User Menu -->
                    <?php if (isLoggedIn()): ?>
                        <?php $user = getCurrentUser(); ?>
                        <div class="relative group">
                            <button class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-semibold text-white hover:text-salon-gold transition-colors">
                                <?php if ($user['role'] === 'ADMIN'): ?>
                                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Admin
                                <?php elseif ($user['role'] === 'STAFF'): ?>
                                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 717.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                    </svg>
                                    Staff
                                <?php else: ?>
                                    <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <?= htmlspecialchars(explode(' ', $user['name'])[0]) ?>
                                <?php endif; ?>
                                <svg class="ml-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div class="absolute right-0 mt-2 w-48 bg-secondary-900 rounded-lg shadow-lg border border-secondary-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                                <div class="py-2">
                                    <a href="<?= $basePath . ($user['role'] === 'ADMIN' ? '/admin' : ($user['role'] === 'STAFF' ? '/staff' : '/customer')) ?>"
                                       class="flex items-center px-4 py-2 text-sm text-white hover:bg-secondary-700 hover:text-salon-gold transition-colors">
                                        <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                                        </svg>
                                        Dashboard
                                    </a>
                                    <?php if ($user['role'] === 'CUSTOMER'): ?>
                                        <a href="<?= $basePath ?>/customer/bookings"
                                           class="flex items-center px-4 py-2 text-sm text-white hover:bg-secondary-700 hover:text-salon-gold transition-colors">
                                            <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 712.25-2.25h13.5A2.25 2.25 0 0721 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 715.25 9h13.5a2.25 2.25 0 712.25 2.25v7.5" />
                                            </svg>
                                            My Bookings
                                        </a>
                                        <a href="<?= $basePath ?>/customer/profile"
                                           class="flex items-center px-4 py-2 text-sm text-white hover:bg-secondary-700 hover:text-salon-gold transition-colors">
                                            <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Profile
                                        </a>
                                    <?php endif; ?>
                                    <div class="border-t border-secondary-700 my-1"></div>
                                    <a href="<?= $basePath ?>/auth/logout.php"
                                       class="flex items-center px-4 py-2 text-sm text-red-400 hover:bg-secondary-700 hover:text-red-300 transition-colors">
                                        <svg class="mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                                        </svg>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/auth/login.php" class="inline-flex items-center rounded-lg px-4 py-2 text-sm font-semibold text-white hover:text-salon-gold transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 616 0z" />
                            </svg>
                            Login
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </header>

    <script>
    // Function to refresh dropdown categories
    function refreshDropdownCategories() {
        const button = event.target;
        const svg = button.querySelector('svg');

        // Add spin animation
        if (svg) {
            svg.classList.add('refresh-spin');
            setTimeout(() => {
                svg.classList.remove('refresh-spin');
            }, 500);
        }

        // Reload the page to get new random categories
        setTimeout(() => {
            window.location.reload();
        }, 300);
    }
    </script>

    <main class="flex-1">
        <!-- Page content will be inserted here -->